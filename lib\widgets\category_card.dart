// lib/widgets/category_card.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'svg_icon.dart';
import '../providers/theme_provider.dart';

class CategoryCard extends StatelessWidget {
  final String title;
  final IconData? icon;
  final String? iconAsset;
  final Color color;
  final VoidCallback onTap;

  const CategoryCard({
    super.key,
    required this.title,
    this.icon,
    this.iconAsset,
    required this.color,
    required this.onTap,
  });

  Widget _buildIcon() {
    // Check if we have an asset path and if it's an SVG
    if (iconAsset != null) {
      if (iconAsset!.toLowerCase().endsWith('.svg')) {
        // Use SvgIcon for SVG files
        return SvgIcon(
          assetPath: iconAsset!,
          width: 45,
          height: 45,
          color: Colors.white,
          fallbackIcon: _getFallbackIcon(),
        );
      } else {
        // Use Image.asset for PNG/other image files
        return Image.asset(
          iconAsset!,
          width: 45,
          height: 45,
          fit: BoxFit.contain,
          color: Colors.white,
          colorBlendMode: BlendMode.srcIn,
          cacheWidth: 45,
          cacheHeight: 45,
          filterQuality: FilterQuality.low,
          errorBuilder: (context, error, stackTrace) {
            debugPrint('Asset failed to load: $iconAsset, using fallback icon');
            IconData fallbackIcon = _getFallbackIcon();
            return Icon(fallbackIcon, size: 45, color: Colors.white);
          },
        );
      }
    } else {
      return Icon(icon ?? Icons.category, size: 45, color: Colors.white);
    }
  }

  IconData _getFallbackIcon() {
    switch (title.toLowerCase()) {
      case 'bold':
        return Icons.flash_on;
      case 'bad':
        return Icons.thumb_down;
      case 'cute':
        return Icons.favorite;
      case 'clever':
        return Icons.lightbulb;
      case 'genius':
        return Icons.psychology;
      case 'dirty':
        return Icons.whatshot;
      case 'flirty':
        return Icons.face;
      case 'hookup':
        return Icons.nightlife;
      case 'romantic':
        return Icons.favorite_border;
      case 'funny':
        return Icons.emoji_emotions;
      case 'nerd':
        return Icons.science;
      case 'food':
        return Icons.restaurant;
      default:
        return Icons.category;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: EdgeInsets.symmetric(vertical: 18, horizontal: 8),
          height: 90,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withValues(alpha: 0.8),
                color,
                color.withValues(alpha: 0.9),
              ],
            ),
            boxShadow: [
              // Reduced shadow for cleaner look
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.15),
                spreadRadius: 0,
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
              // Subtle colored shadow
              BoxShadow(
                color: color.withValues(alpha: 0.15),
                spreadRadius: 0,
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: onTap,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              color: Colors.black.withValues(alpha: 0.3),
                              offset: Offset(1, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(width: 20),
                    Container(
                      width: 70,
                      height: 70,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(18),
                        boxShadow: [
                          // Reduced icon container shadow
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            spreadRadius: 0,
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Center(child: _buildIcon()),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
