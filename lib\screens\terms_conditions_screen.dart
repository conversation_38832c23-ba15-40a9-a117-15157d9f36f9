// lib/screens/terms_conditions_screen.dart
// Terms and Conditions screen for Play Store compliance

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';

class TermsConditionsScreen extends StatelessWidget {
  const TermsConditionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor: themeProvider.isDarkMode 
              ? const Color(0xFF121212) 
              : Colors.grey[50],
          appBar: AppBar(
            title: const Text(
              'Terms and Conditions',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 20,
              ),
            ),
            backgroundColor: themeProvider.isDarkMode 
                ? const Color(0xFF1E1E1E) 
                : Colors.white,
            foregroundColor: themeProvider.isDarkMode 
                ? Colors.white 
                : Colors.black87,
            elevation: 2,
            shadowColor: Colors.black26,
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: themeProvider.isDarkMode
                    ? [
                        const Color(0xFF121212),
                        const Color(0xFF1E1E1E),
                      ]
                    : [
                        Colors.grey[50]!,
                        Colors.white,
                      ],
              ),
            ),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Container(
                decoration: BoxDecoration(
                  color: themeProvider.isDarkMode 
                      ? const Color(0xFF2D2D2D) 
                      : Colors.white,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      spreadRadius: 0,
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSection(
                      'Acceptance of Terms',
                      'By downloading and using Charm Shots, you agree to be bound by these terms and conditions.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Use of the App',
                      'Charm Shots is intended for entertainment purposes only. The pickup lines are meant to be fun and lighthearted. Use them responsibly and respectfully.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Content Guidelines',
                      'All content in the app is designed to be family-friendly and appropriate. We do not tolerate harassment, inappropriate behavior, or misuse of the content.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Intellectual Property',
                      'The content, design, and functionality of Charm Shots are protected by copyright and other intellectual property laws.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Disclaimer',
                      'The app is provided "as is" without warranties. We are not responsible for any outcomes from using the pickup lines.',
                      themeProvider,
                    ),
                    _buildSection(
                      'User Responsibility',
                      'Users are responsible for using the app content appropriately and respectfully. Always respect others\' boundaries and consent.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Updates and Changes',
                      'We may update these terms from time to time. Continued use of the app constitutes acceptance of any changes.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Contact Information',
                      'For questions about these terms, please contact us through the app store.',
                      themeProvider,
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'Last updated: ${DateTime.now().toString().split(' ')[0]}',
                      style: TextStyle(
                        fontSize: 12,
                        color: themeProvider.isDarkMode 
                            ? Colors.grey[400] 
                            : Colors.grey[600],
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSection(String title, String content, ThemeProvider themeProvider) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: themeProvider.isDarkMode 
                  ? Colors.white 
                  : Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: TextStyle(
              fontSize: 14,
              height: 1.5,
              color: themeProvider.isDarkMode 
                  ? Colors.grey[300] 
                  : Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }
}
