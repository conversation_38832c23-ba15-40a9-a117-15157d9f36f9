// lib/screens/privacy_policy_screen.dart
// Privacy Policy screen for Play Store compliance

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor: themeProvider.isDarkMode
              ? const Color(0xFF121212)
              : Colors.grey[50],
          appBar: AppBar(
            title: const Text(
              'Privacy Policy',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
            ),
            backgroundColor: themeProvider.isDarkMode
                ? const Color(0xFF1E1E1E)
                : Colors.white,
            foregroundColor: themeProvider.isDarkMode
                ? Colors.white
                : Colors.black87,
            elevation: 2,
            shadowColor: Colors.black26,
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: themeProvider.isDarkMode
                    ? [const Color(0xFF121212), const Color(0xFF1E1E1E)]
                    : [Colors.grey[50]!, Colors.white],
              ),
            ),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Container(
                decoration: BoxDecoration(
                  color: themeProvider.isDarkMode
                      ? const Color(0xFF2D2D2D)
                      : Colors.white,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      spreadRadius: 0,
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSection(
                      'Information We Collect',
                      'Charm Shots does not collect, store, or transmit any personal information. The app works entirely offline and does not require any personal data to function.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Data Usage',
                      'All pickup lines and content are stored locally on your device. No data is sent to external servers or third parties.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Permissions',
                      'The app may request minimal permissions for basic functionality such as storage access for sharing features. These permissions are used solely for app functionality.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Third-Party Services',
                      'Charm Shots does not integrate with any third-party analytics, advertising, or data collection services.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Children\'s Privacy',
                      'Our app is designed to be family-friendly and does not knowingly collect information from children under 13.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Changes to Privacy Policy',
                      'We may update this privacy policy from time to time. Any changes will be reflected in the app update.',
                      themeProvider,
                    ),
                    _buildSection(
                      'Contact Us',
                      'If you have any questions about this privacy policy, please contact us through the app store.',
                      themeProvider,
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'Last updated: ${DateTime.now().toString().split(' ')[0]}',
                      style: TextStyle(
                        fontSize: 12,
                        color: themeProvider.isDarkMode
                            ? Colors.grey[400]
                            : Colors.grey[600],
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSection(
    String title,
    String content,
    ThemeProvider themeProvider,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: TextStyle(
              fontSize: 14,
              height: 1.5,
              color: themeProvider.isDarkMode
                  ? Colors.grey[300]
                  : Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }
}
