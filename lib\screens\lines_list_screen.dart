// lib/screens/lines_list_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';
import '../utils/image_cache_manager.dart';
import '../utils/performance_utils.dart';
import '../utils/background_manager.dart';
import '../providers/theme_provider.dart';
import '../providers/favorites_provider.dart';
import '../data/pickup_lines_data.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/enhanced_edit_dialog.dart';
import '../models/styled_text.dart';

class LinesListScreen extends StatefulWidget {
  final String category;
  final String language;
  const LinesListScreen({
    super.key,
    required this.category,
    required this.language,
  });

  @override
  State<LinesListScreen> createState() => _LinesListScreenState();
}

class _LinesListScreenState extends State<LinesListScreen> {
  // Background images managed by BackgroundManager (supports future expansion to 20+)
  final List<String> backgroundImages = BackgroundManager.getAllBackgrounds();

  // Track background index for each card using ValueNotifiers for better performance
  final Map<int, ValueNotifier<int>> cardBackgroundNotifiers = {};
  // Track initial random backgrounds for each post
  final Map<int, String> initialBackgrounds = {};
  // Track styled text for each quote
  final Map<int, StyledText> styledTexts = {};
  DateTime? _lastTapTime; // For throttling rapid taps

  // Pickup lines loaded based on category
  List<String> lines = [];

  void _changeBackground(int index) {
    // Throttle rapid taps to prevent performance issues
    final now = DateTime.now();
    if (_lastTapTime != null &&
        now.difference(_lastTapTime!).inMilliseconds < 200) {
      return; // Ignore rapid taps
    }
    _lastTapTime = now;

    // Play tap sound
    HapticFeedback.lightImpact();

    // Get or create ValueNotifier for this card
    if (!cardBackgroundNotifiers.containsKey(index)) {
      cardBackgroundNotifiers[index] = ValueNotifier<int>(0);
    }

    final notifier = cardBackgroundNotifiers[index]!;
    final currentBg = notifier.value;
    final newBg = (currentBg + 1) % backgroundImages.length;

    // Update only the specific card, not the entire widget
    notifier.value = newBg;
  }

  void _updateQuote(int index, String newQuote, [TextStyle? newStyle]) {
    setState(() {
      lines[index] = newQuote;
      if (newStyle != null) {
        styledTexts[index] = StyledText.fromTextStyle(newQuote, newStyle);
      }
      // Debug output
      if (kDebugMode) {
        print('Updated quote at index $index: $newQuote');
      }
    });
  }

  @override
  void initState() {
    super.initState();
    // Load pickup lines for the specific category and language
    // Create a mutable copy to allow editing
    lines = List<String>.from(
      PickupLinesData.getLinesForCategory(widget.category, widget.language),
    );

    // Assign random initial backgrounds for each post
    _initializeRandomBackgrounds();

    // Preload images when this screen loads (lazy loading)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _preloadImagesLazily();
    });
  }

  void _initializeRandomBackgrounds() {
    for (int i = 0; i < lines.length; i++) {
      // Use post content hash for consistent background per post
      final postBackground = BackgroundManager.getBackgroundForPost(lines[i]);
      initialBackgrounds[i] = postBackground;

      // Initialize background notifier with random starting index
      final backgroundIndex = backgroundImages.indexOf(postBackground);
      cardBackgroundNotifiers[i] = ValueNotifier<int>(
        backgroundIndex >= 0 ? backgroundIndex : 0,
      );
    }
  }

  void _preloadImagesLazily() {
    // Only preload when user actually needs them
    Future.delayed(const Duration(milliseconds: 500), () {
      OptimizedImageCache().preloadBackgroundImages().catchError((error) {
        if (kDebugMode) {
          debugPrint('Background image preload failed: $error');
        }
      });
    });
  }

  @override
  void dispose() {
    // Clean up ValueNotifiers
    for (final notifier in cardBackgroundNotifiers.values) {
      notifier.dispose();
    }
    cardBackgroundNotifiers.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<ThemeProvider, FavoritesProvider>(
      builder: (context, themeProvider, favoritesProvider, child) {
        return Scaffold(
          backgroundColor: themeProvider.isDarkMode
              ? Colors.grey.shade900
              : Colors.white,
          appBar: Custom3DAppBar(title: widget.category),
          body: Container(
            color: themeProvider.isDarkMode
                ? Colors.grey.shade900
                : Colors.white,
            child: PerformanceUtils.createOptimizedListView(
              padding: EdgeInsets.only(left: 16, right: 16, bottom: 16),
              itemCount: lines.length,
              itemExtent: 404, // Fixed height: 380 + 24 margin
              cacheExtent: 200, // Reduced cache to save memory
              itemBuilder: (context, index) {
                // Ensure background notifier exists (fallback for edge cases)
                cardBackgroundNotifiers.putIfAbsent(index, () {
                  // If not initialized, use random background
                  final randomBg = BackgroundManager.getBackgroundForPost(
                    lines[index],
                  );
                  final bgIndex = backgroundImages.indexOf(randomBg);
                  return ValueNotifier<int>(bgIndex >= 0 ? bgIndex : 0);
                });

                return QuoteCard(
                  key: ValueKey('quote_card_${index}_${lines[index].hashCode}'),
                  index: index,
                  quote: lines[index],
                  styledText: styledTexts[index],
                  backgroundImages: backgroundImages,
                  backgroundNotifier: cardBackgroundNotifiers[index]!,
                  onBackgroundChange: () => _changeBackground(index),
                  onQuoteUpdate: (newQuote, newStyle) =>
                      _updateQuote(index, newQuote, newStyle),
                );
              },
            ),
          ),
        );
      },
    );
  }
}

class QuoteCard extends StatefulWidget {
  final int index;
  final String quote;
  final StyledText? styledText;
  final List<String> backgroundImages;
  final ValueNotifier<int> backgroundNotifier;
  final VoidCallback onBackgroundChange;
  final Function(String, TextStyle?) onQuoteUpdate;

  const QuoteCard({
    super.key,
    required this.index,
    required this.quote,
    this.styledText,
    required this.backgroundImages,
    required this.backgroundNotifier,
    required this.onBackgroundChange,
    required this.onQuoteUpdate,
  });

  @override
  State<QuoteCard> createState() => _QuoteCardState();
}

class _QuoteCardState extends State<QuoteCard> {
  // Screenshot controller for this card
  final ScreenshotController _screenshotController = ScreenshotController();

  // Save image to gallery with permission handling
  Future<void> _saveToGallery() async {
    try {
      // Request storage permission
      PermissionStatus permission;
      if (Platform.isAndroid) {
        if (await Permission.storage.isDenied) {
          permission = await Permission.storage.request();
        } else {
          permission = PermissionStatus.granted;
        }

        // For Android 13+ (API 33+), use photos permission
        if (await Permission.photos.isDenied) {
          permission = await Permission.photos.request();
        }
      } else {
        permission = PermissionStatus.granted;
      }

      if (permission == PermissionStatus.granted) {
        // Capture screenshot
        final Uint8List? imageBytes = await _screenshotController.capture();

        if (imageBytes != null) {
          // Save to gallery
          final result = await ImageGallerySaver.saveImage(
            imageBytes,
            name: "charm_shot_${DateTime.now().millisecondsSinceEpoch}",
            quality: 100,
          );

          if (mounted) {
            if (result['isSuccess'] == true) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Image saved to gallery!'),
                  backgroundColor: Colors.green,
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to save image'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Storage permission required to save images'),
              backgroundColor: Colors.orange,
              action: SnackBarAction(
                label: 'Settings',
                onPressed: () => openAppSettings(),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Cache expensive decorations to avoid recreating them on every build
  static final _cardDecoration = BoxDecoration(
    borderRadius: BorderRadius.circular(24),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withValues(alpha: 0.1),
        spreadRadius: 0,
        blurRadius: 4,
        offset: const Offset(0, 2),
      ),
    ],
  );

  static final _editButtonDecoration = BoxDecoration(
    color: Colors.white,
    shape: BoxShape.circle,
    boxShadow: [
      BoxShadow(
        color: Colors.black.withValues(alpha: 0.05),
        blurRadius: 2,
        offset: const Offset(0, 1),
      ),
    ],
  );

  static final _borderRadius = BorderRadius.circular(24);
  static final _overlayColor = Colors.black.withValues(alpha: 0.4);

  void _showEditDialog(BuildContext context, int index, String currentQuote) {
    showEnhancedEditDialog(
      context: context,
      initialText: currentQuote,
      onSave: (newText, newStyle) {
        if (kDebugMode) {
          print('Edit dialog onSave called with: $newText');
        }
        widget.onQuoteUpdate(newText, newStyle);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Quote updated with custom styling!'),
            backgroundColor: Colors.green,
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<int>(
      valueListenable: widget.backgroundNotifier,
      builder: (context, currentBackgroundIndex, child) {
        String currentBg =
            widget.backgroundImages[currentBackgroundIndex %
                widget.backgroundImages.length];

        return Consumer<FavoritesProvider>(
          builder: (context, favoritesProvider, child) {
            final postId = '${widget.index}_${widget.quote.hashCode}';
            final isFavorite = favoritesProvider.isFavorite(postId);

            return GestureDetector(
              onTap: () {
                // Make post reactive - change background and play sound
                widget.onBackgroundChange();
                if (favoritesProvider.tapSoundEnabled) {
                  SystemSound.play(SystemSoundType.click);
                }
              },
              child: Screenshot(
                controller: _screenshotController,
                child: Container(
                  margin: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                  height: 380, // Optimized height
                  decoration: _cardDecoration,
                child: ClipRRect(
                  borderRadius: _borderRadius,
                  child: Stack(
                    children: [
                      // Background Image - Optimized with caching
                      Positioned.fill(
                        child: OptimizedAssetImage(
                          imagePath: currentBg,
                          fit: BoxFit.cover,
                          filterQuality: FilterQuality.low,
                          errorWidget: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.deepPurple.shade400,
                                  Colors.purple.shade600,
                                  Colors.pink.shade500,
                                ],
                              ),
                            ),
                            child: Center(
                              child: Icon(
                                Icons.favorite,
                                color: Colors.white54,
                                size: 48,
                              ),
                            ),
                          ),
                        ),
                      ),
                      // Dark overlay for better text readability
                      Container(
                        width: double.infinity,
                        height: double.infinity,
                        color: _overlayColor,
                      ),
                      // Content
                      Positioned.fill(
                        child: GestureDetector(
                          onTap: widget.onBackgroundChange,
                          child: Container(
                            padding: EdgeInsets.all(12),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                // Edit button (top right)
                                Align(
                                  alignment: Alignment.topRight,
                                  child: GestureDetector(
                                    onTap: () => _showEditDialog(
                                      context,
                                      widget.index,
                                      widget.quote,
                                    ),
                                    child: Container(
                                      width: 36,
                                      height: 36,
                                      decoration: _editButtonDecoration,
                                      child: Icon(
                                        Icons.edit,
                                        color: Colors.black,
                                        size: 18,
                                      ),
                                    ),
                                  ),
                                ),
                                // Quote content
                                Expanded(
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                      vertical: 12,
                                      horizontal: 8,
                                    ),
                                    child: Center(
                                      child: RichText(
                                        textAlign: TextAlign.center,
                                        text: TextSpan(
                                          style:
                                              widget.styledText
                                                  ?.toTextStyle() ??
                                              TextStyle(
                                                fontSize: 18,
                                                color: Colors.white,
                                                fontWeight: FontWeight.bold,
                                                height: 1.3,
                                                shadows: [
                                                  Shadow(
                                                    color: Colors.black
                                                        .withValues(alpha: 0.5),
                                                    offset: Offset(1, 1),
                                                    blurRadius: 2,
                                                  ),
                                                ],
                                              ),
                                          children: [
                                            TextSpan(
                                              text: '"',
                                              style: TextStyle(
                                                fontSize:
                                                    (widget
                                                            .styledText
                                                            ?.fontSize ??
                                                        18) *
                                                    1.3,
                                                fontWeight: FontWeight.w900,
                                                color:
                                                    widget
                                                        .styledText
                                                        ?.textColor ??
                                                    Colors.white,
                                                shadows: [
                                                  Shadow(
                                                    color: Colors.black
                                                        .withValues(alpha: 0.5),
                                                    offset: Offset(1, 1),
                                                    blurRadius: 2,
                                                  ),
                                                ],
                                              ),
                                            ),
                                            TextSpan(text: widget.quote),
                                            TextSpan(
                                              text: '"',
                                              style: TextStyle(
                                                fontSize:
                                                    (widget
                                                            .styledText
                                                            ?.fontSize ??
                                                        18) *
                                                    1.3,
                                                fontWeight: FontWeight.w900,
                                                color:
                                                    widget
                                                        .styledText
                                                        ?.textColor ??
                                                    Colors.white,
                                                shadows: [
                                                  Shadow(
                                                    color: Colors.black
                                                        .withValues(alpha: 0.5),
                                                    offset: Offset(1, 1),
                                                    blurRadius: 2,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                // Action buttons
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    vertical: 6,
                                    horizontal: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      _buildActionButton(
                                        icon: isFavorite
                                            ? Icons.favorite
                                            : Icons.favorite_border,
                                        label: isFavorite ? 'Liked' : 'Like',
                                        onTap: () {
                                          favoritesProvider.toggleFavorite(
                                            postId,
                                            widget.quote,
                                            currentBg,
                                          );
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                isFavorite
                                                    ? 'Removed from favorites!'
                                                    : 'Added to favorites!',
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                      _buildActionButton(
                                        icon: Icons.download,
                                        label: 'Save',
                                        onTap: () => _saveToGallery(),
                                      ),
                                      _buildActionButton(
                                        icon: Icons.copy,
                                        label: 'Copy',
                                        onTap: () {
                                          Clipboard.setData(
                                            ClipboardData(text: widget.quote),
                                          );
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                'Copied to clipboard!',
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                      _buildActionButton(
                                        icon: Icons.share,
                                        label: 'Share',
                                        onTap: () {
                                          // Share logic - using basic share for now
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                'Share functionality',
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.black, size: 24),
          SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: Colors.black,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
