// lib/screens/language_selection_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/app_drawer.dart';
import '../widgets/custom_app_bar.dart';
import '../providers/theme_provider.dart';

class LanguageSelectScreen extends StatelessWidget {
  const LanguageSelectScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          appBar: Custom3DAppBar(title: 'Charm Shot'),
          drawer: AppDrawer(),
          body: Container(
            color: themeProvider.isDarkMode
                ? Colors.grey.shade900
                : Colors.white,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Hindi Language Button with Enhanced 3D Effects
                  _build3DLanguageButton(
                    context: context,
                    themeProvider: themeProvider,
                    flag: '🇮🇳',
                    language: 'Hindi',
                    color: Colors.blue,
                    onPressed: () => Navigator.pushNamed(
                      context,
                      '/categories',
                      arguments: 'Hindi',
                    ),
                  ),
                  SizedBox(height: 48),
                  // English Language Button with Enhanced 3D Effects
                  _build3DLanguageButton(
                    context: context,
                    themeProvider: themeProvider,
                    flag: '🇬🇧',
                    language: 'English',
                    color: Colors.teal,
                    onPressed: () => Navigator.pushNamed(
                      context,
                      '/categories',
                      arguments: 'English',
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _build3DLanguageButton({
    required BuildContext context,
    required ThemeProvider themeProvider,
    required String flag,
    required String language,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: 340,
      height: 100,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withValues(alpha: 0.9),
            color,
            Color.lerp(color, Colors.white, 0.1) ?? color,
          ],
        ),
        boxShadow: [
          // Enhanced 3D shadow with multiple layers
          ...themeProvider.get3DShadow(elevation: 16.0),
          // Additional colored shadow for depth
          BoxShadow(
            color: color.withValues(alpha: 0.4),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
          // Subtle glow effect
          BoxShadow(
            color: color.withValues(alpha: 0.3),
            spreadRadius: 2,
            blurRadius: 30,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(32),
          onTap: onPressed,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Flag with enhanced shadow
                Container(
                  decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Text(flag, style: TextStyle(fontSize: 40)),
                ),
                SizedBox(width: 16),
                // Language text with shadow
                Text(
                  language,
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        color: Colors.black.withValues(alpha: 0.5),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                      Shadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 16,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
