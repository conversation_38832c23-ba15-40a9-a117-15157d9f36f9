// lib/screens/todays_shots_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:math';
import 'dart:io';
import '../utils/image_cache_manager.dart';
import '../utils/background_manager.dart';
import '../widgets/app_drawer.dart';
import '../widgets/custom_app_bar.dart';
import '../providers/theme_provider.dart';
import '../providers/favorites_provider.dart';
import '../data/pickup_lines_data.dart';

class TodaysShotsScreen extends StatefulWidget {
  const TodaysShotsScreen({super.key});

  @override
  State<TodaysShotsScreen> createState() => _TodaysShotsScreenState();
}

class _TodaysShotsScreenState extends State<TodaysShotsScreen> {
  final Random _random = Random();

  // Screenshot controller for saving images
  final ScreenshotController _screenshotController = ScreenshotController();

  // Random pickup lines collection from all categories
  List<String> _pickupLines = [];

  // Background images managed by BackgroundManager (supports future expansion to 20+)
  final List<String> backgroundImages = BackgroundManager.getAllBackgrounds();

  String _currentLine = "";
  String _currentBackground = "";

  @override
  void initState() {
    super.initState();
    // Load pickup lines from all categories
    _loadAllPickupLines();
    _generateRandomShot();
  }

  void _loadAllPickupLines() {
    // Load all pickup lines from both languages
    _pickupLines = PickupLinesData.getAllLines();
  }

  void _generateRandomShot() {
    setState(() {
      _currentLine = _pickupLines[_random.nextInt(_pickupLines.length)];
      // Use BackgroundManager for better random background selection
      _currentBackground = BackgroundManager.getRandomBackgroundExcluding(
        _currentBackground,
      );
    });
  }

  // Save image to gallery with permission handling
  Future<void> _saveToGallery() async {
    try {
      // Request storage permission
      PermissionStatus permission;
      if (Platform.isAndroid) {
        if (await Permission.storage.isDenied) {
          permission = await Permission.storage.request();
        } else {
          permission = PermissionStatus.granted;
        }

        // For Android 13+ (API 33+), use photos permission
        if (await Permission.photos.isDenied) {
          permission = await Permission.photos.request();
        }
      } else {
        permission = PermissionStatus.granted;
      }

      if (permission == PermissionStatus.granted) {
        // Capture screenshot
        final Uint8List? imageBytes = await _screenshotController.capture();

        if (imageBytes != null) {
          // Save to gallery
          final result = await ImageGallerySaver.saveImage(
            imageBytes,
            name: "charm_shot_${DateTime.now().millisecondsSinceEpoch}",
            quality: 100,
          );

          if (mounted) {
            if (result['isSuccess'] == true) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Image saved to gallery!'),
                  backgroundColor: Colors.green,
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to save image'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Storage permission required to save images'),
              backgroundColor: Colors.orange,
              action: SnackBarAction(
                label: 'Settings',
                onPressed: () => openAppSettings(),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<ThemeProvider, FavoritesProvider>(
      builder: (context, themeProvider, favoritesProvider, child) {
        return Scaffold(
          backgroundColor: themeProvider.isDarkMode
              ? Colors.grey.shade900
              : Colors.white,
          appBar: Custom3DAppBar(
            title: "Today's Shots",
            actions: [
              IconButton(
                icon: Icon(Icons.refresh),
                onPressed: _generateRandomShot,
                tooltip: 'Get New Shot',
              ),
            ],
          ),
          drawer: AppDrawer(),
          body: Container(
            color: Colors.white,
            child: Center(
              child: GestureDetector(
                onTap: () {
                  // Make post reactive - change background and play sound
                  _generateRandomShot();
                  if (favoritesProvider.tapSoundEnabled) {
                    SystemSound.play(SystemSoundType.click);
                  }
                },
                child: Screenshot(
                  controller: _screenshotController,
                  child: Container(
                    margin: EdgeInsets.all(16),
                    height: 500,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          spreadRadius: 0,
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          spreadRadius: 0,
                          blurRadius: 16,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(24),
                      child: Stack(
                        children: [
                          // Background Image
                          Positioned.fill(
                            child: OptimizedAssetImage(
                              imagePath: _currentBackground,
                              fit: BoxFit.cover,
                              filterQuality: FilterQuality.low,
                              errorWidget: Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      Colors.deepPurple.shade400,
                                      Colors.purple.shade600,
                                      Colors.pink.shade500,
                                    ],
                                  ),
                                ),
                                child: Center(
                                  child: Icon(
                                    Icons.favorite,
                                    color: Colors.white54,
                                    size: 48,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          // Dark overlay for better text readability
                          Container(
                            width: double.infinity,
                            height: double.infinity,
                            color: Colors.black.withValues(alpha: 0.4),
                          ),
                          // Content
                          Positioned.fill(
                            child: Container(
                              padding: EdgeInsets.all(24),
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  // Header
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      vertical: 12,
                                      horizontal: 16,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withValues(
                                        alpha: 0.9,
                                      ),
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.auto_awesome,
                                          color: Colors.orange,
                                          size: 20,
                                        ),
                                        SizedBox(width: 8),
                                        Text(
                                          "Today's Random Shot",
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.black87,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  // Quote content
                                  Expanded(
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                        vertical: 20,
                                        horizontal: 16,
                                      ),
                                      child: Center(
                                        child: RichText(
                                          textAlign: TextAlign.center,
                                          text: TextSpan(
                                            style: TextStyle(
                                              fontSize: 20,
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                              height: 1.4,
                                              shadows: [
                                                Shadow(
                                                  color: Colors.black
                                                      .withValues(alpha: 0.5),
                                                  offset: Offset(1, 1),
                                                  blurRadius: 2,
                                                ),
                                              ],
                                            ),
                                            children: [
                                              TextSpan(
                                                text: '"',
                                                style: TextStyle(
                                                  fontSize: 28,
                                                  fontWeight: FontWeight.w900,
                                                ),
                                              ),
                                              TextSpan(text: _currentLine),
                                              TextSpan(
                                                text: '"',
                                                style: TextStyle(
                                                  fontSize: 28,
                                                  fontWeight: FontWeight.w900,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  // Action buttons
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      vertical: 8,
                                      horizontal: 12,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceEvenly,
                                      children: [
                                        _buildActionButton(
                                          icon: Icons.refresh,
                                          label: 'New',
                                          onTap: _generateRandomShot,
                                        ),
                                        Builder(
                                          builder: (context) {
                                            final postId =
                                                'today_${_currentLine.hashCode}';
                                            final isFavorite = favoritesProvider
                                                .isFavorite(postId);
                                            return _buildActionButton(
                                              icon: isFavorite
                                                  ? Icons.favorite
                                                  : Icons.favorite_border,
                                              label: isFavorite
                                                  ? 'Liked'
                                                  : 'Like',
                                              onTap: () {
                                                favoritesProvider
                                                    .toggleFavorite(
                                                      postId,
                                                      _currentLine,
                                                      _currentBackground,
                                                    );
                                                ScaffoldMessenger.of(
                                                  context,
                                                ).showSnackBar(
                                                  SnackBar(
                                                    content: Text(
                                                      isFavorite
                                                          ? 'Removed from favorites!'
                                                          : 'Added to favorites!',
                                                    ),
                                                  ),
                                                );
                                              },
                                            );
                                          },
                                        ),
                                        _buildActionButton(
                                          icon: Icons.copy,
                                          label: 'Copy',
                                          onTap: () {
                                            Clipboard.setData(
                                              ClipboardData(text: _currentLine),
                                            );
                                            ScaffoldMessenger.of(
                                              context,
                                            ).showSnackBar(
                                              SnackBar(
                                                content: Text(
                                                  'Copied to clipboard!',
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                        _buildActionButton(
                                          icon: Icons.download,
                                          label: 'Save',
                                          onTap: _saveToGallery,
                                        ),
                                        _buildActionButton(
                                          icon: Icons.share,
                                          label: 'Share',
                                          onTap: () {
                                            ScaffoldMessenger.of(
                                              context,
                                            ).showSnackBar(
                                              SnackBar(
                                                content: Text(
                                                  'Share functionality',
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.black, size: 24),
          SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: Colors.black,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
