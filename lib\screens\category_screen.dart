// lib/screens/category_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/app_drawer.dart';
import '../widgets/category_card.dart';
import '../widgets/custom_app_bar.dart';
import '../providers/theme_provider.dart';

final categories = [
  {
    'name': 'Bold',
    'iconAsset': 'assets/icons/bold.svg',
    'color': Colors.deepOrange,
  },
  {'name': 'Bad', 'icon': Icons.thumb_down, 'color': Colors.blueGrey},
  {
    'name': 'Cute',
    'iconAsset': 'assets/icons/cute.svg',
    'color': Colors.pinkAccent,
  },
  {
    'name': 'Clever',
    'iconAsset': 'assets/icons/cleaver.svg',
    'color': Colors.purple,
  },
  {
    'name': 'Genius',
    'iconAsset': 'assets/icons/genius.svg',
    'color': Colors.indigo,
  },
  {
    'name': 'Dirty',
    'iconAsset': 'assets/icons/dirty.svg',
    'color': Colors.green,
  },
  {
    'name': 'Flirty',
    'iconAsset': 'assets/icons/flirty.svg',
    'color': Colors.redAccent,
  },
  {
    'name': 'Hookup',
    'iconAsset': 'assets/icons/hookup.svg',
    'color': Colors.teal,
  },
  {
    'name': 'Romantic',
    'iconAsset': 'assets/icons/romantic.svg',
    'color': Colors.red,
  },
  {
    'name': 'Funny',
    'iconAsset': 'assets/icons/funny.svg',
    'color': Colors.amber,
  },
  {'name': 'Nerd', 'iconAsset': 'assets/icons/nerd.svg', 'color': Colors.cyan},
  {
    'name': 'Food',
    'iconAsset': 'assets/icons/food.svg',
    'color': Colors.orange,
  },
];

class CategoryScreen extends StatelessWidget {
  final String language;
  const CategoryScreen({super.key, required this.language});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          appBar: Custom3DAppBar(
            title: 'Pickup Lines',
            actions: [
              IconButton(
                icon: Icon(Icons.favorite),
                onPressed: () => Navigator.pushNamed(context, '/favorites'),
              ),
            ],
          ),
          drawer: AppDrawer(),
          backgroundColor: themeProvider.isDarkMode
              ? Colors.grey.shade900
              : Colors.white,
          body: Container(
            color: themeProvider.isDarkMode
                ? Colors.grey.shade900
                : Colors.white,
            child: ListView.builder(
              padding: EdgeInsets.all(16),
              itemCount: categories.length,
              itemBuilder: (context, index) {
                final cat = categories[index];
                return CategoryCard(
                  title: cat['name'] as String,
                  icon: cat['icon'] as IconData?,
                  iconAsset: cat['iconAsset'] as String?,
                  color: cat['color'] as Color,
                  onTap: () => Navigator.pushNamed(
                    context,
                    '/lines',
                    arguments: {
                      'category': cat['name'] as String,
                      'language': language,
                    },
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }
}
